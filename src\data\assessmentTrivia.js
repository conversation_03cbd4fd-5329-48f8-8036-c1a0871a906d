/**
 * Assessment Trivia Data
 * Contains interesting facts about RIASEC, OCEAN (Big Five), and VIA assessments
 * Plus insights about AI advantages in psychological assessment
 */

export const assessmentTrivia = {
  riasec: [
    {
      id: 'riasec_1',
      title: 'Asal Usul RIASEC (1959)',
      content: 'Model RIASEC dikembangkan oleh psikolog John Holland pada tahun 1959. Holland percaya bahwa "kepribadian adalah takdir karier" - pilihan pekerjaan mencerminkan kepribadian kita.',
      category: 'history'
    },
    {
      id: 'riasec_2',
      title: 'Hexagon Holland yang Genius',
      content: 'RIASEC digambarkan sebagai hexagon karena tipe yang berdekatan (seperti Realistic-Investigative) lebih kompatibel 73% daripada yang berseberangan (seperti Social-Realistic).',
      category: 'theory'
    },
    {
      id: 'riasec_3',
      title: 'Kombinasi Paling Langka',
      content: 'Tipe Artistic-Investigative hanya ditemukan pada 2.1% populasi - biasanya peneli<PERSON> kreati<PERSON>, desainer U<PERSON>, atau arsitek yang menggabungkan seni dengan sains.',
      category: 'statistics'
    },
    {
      id: 'riasec_4',
      title: 'Akurasi Prediksi Karier 85%',
      content: 'Penelitian 40+ tahun menunjukkan RIASEC dapat memprediksi kepuasan kerja dengan akurasi 85% ketika seseorang bekerja di lingkungan yang sesuai tipe kepribadiannya.',
      category: 'research'
    },
    {
      id: 'riasec_5',
      title: 'Evolusi Kepribadian Karier',
      content: 'Profil RIASEC dapat berubah 15-25% seiring waktu, terutama setelah pengalaman kerja signifikan, pendidikan lanjut, atau life-changing events.',
      category: 'development'
    },
    {
      id: 'riasec_6',
      title: 'Realistic: Tipe Paling Umum',
      content: 'Tipe Realistic (praktis, hands-on) adalah yang paling umum di dunia - sekitar 38% populasi, terutama di negara berkembang dengan sektor manufaktur besar.',
      category: 'statistics'
    },
    {
      id: 'riasec_7',
      title: 'Social vs Enterprising',
      content: 'Orang Social cenderung memilih karier helping (guru, perawat), sementara Enterprising memilih leading (CEO, sales). Keduanya people-oriented tapi dengan fokus berbeda.',
      category: 'theory'
    }
  ],

  ocean: [
    {
      id: 'ocean_1',
      title: 'Big Five: Model Terpercaya #1',
      content: 'OCEAN (Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism) adalah model kepribadian paling valid secara ilmiah - digunakan 89% psikolog dunia.',
      category: 'theory'
    },
    {
      id: 'ocean_2',
      title: 'DNA vs Lingkungan: 50-50',
      content: 'Sekitar 40-60% skor Big Five ditentukan genetika (DNA orangtua), sisanya 40-60% dibentuk lingkungan, pengalaman, dan pilihan hidup kita.',
      category: 'genetics'
    },
    {
      id: 'ocean_3',
      title: 'Conscientiousness > IQ',
      content: 'Conscientiousness (kedisiplinan) adalah prediktor kesuksesan #1 - bahkan mengalahkan IQ dalam memprediksi prestasi akademik dan karier jangka panjang.',
      category: 'success'
    },
    {
      id: 'ocean_4',
      title: 'Extraversion: Barat vs Timur',
      content: 'Skor Extraversion 23% lebih tinggi di budaya individualistik (AS, Eropa) dibanding kolektivistik (Jepang, Korea) - budaya membentuk kepribadian.',
      category: 'culture'
    },
    {
      id: 'ocean_5',
      title: 'Neuroticism = Kreativitas?',
      content: 'Neuroticism moderat (bukan tinggi/rendah) dikaitkan dengan kreativitas tertinggi - sedikit kecemasan memicu inovasi, terlalu banyak melumpuhkan.',
      category: 'creativity'
    },
    {
      id: 'ocean_6',
      title: 'Kepribadian Berubah Seiring Usia',
      content: 'Conscientiousness dan Agreeableness naik seiring usia (kita jadi lebih disiplin & ramah), Neuroticism dan Openness turun (lebih stabil & praktis).',
      category: 'aging'
    },
    {
      id: 'ocean_7',
      title: 'Openness: Kunci Adaptasi',
      content: 'Orang dengan Openness tinggi 67% lebih mudah beradaptasi dengan perubahan teknologi dan tren industri - kunci survival di era digital.',
      category: 'success'
    },
    {
      id: 'ocean_8',
      title: 'Agreeableness: Pedang Bermata Dua',
      content: 'Agreeableness tinggi bagus untuk teamwork tapi bisa merugikan dalam negosiasi gaji - orang agreeable rata-rata bergaji 18% lebih rendah.',
      category: 'research'
    }
  ],

  via: [
    {
      id: 'via_1',
      title: 'VIA: Revolusi Psikologi Positif',
      content: 'VIA (Values in Action) dikembangkan Martin Seligman & Christopher Peterson sebagai "anti-DSM" - fokus pada kekuatan karakter, bukan gangguan mental.',
      category: 'history'
    },
    {
      id: 'via_2',
      title: '24 Kekuatan, 6 Kebajikan Universal',
      content: 'VIA mengidentifikasi 24 kekuatan karakter dalam 6 kebajikan universal: Wisdom (kebijaksanaan), Courage (keberanian), Humanity (kemanusiaan), Justice (keadilan), Temperance (pengendalian diri), Transcendence (transendensi).',
      category: 'structure'
    },
    {
      id: 'via_3',
      title: 'Signature Strengths: Top 3-5 Anda',
      content: 'Setiap orang memiliki 3-5 "signature strengths" - kekuatan yang paling autentik, energizing, dan mudah digunakan. Ini adalah "superpower" alami Anda.',
      category: 'concept'
    },
    {
      id: 'via_4',
      title: 'Universal di 200+ Negara',
      content: 'Ke-24 kekuatan VIA ditemukan di semua 200+ negara, budaya, dan agama di dunia - membuktikan sifat universal karakter manusia yang baik.',
      category: 'culture'
    },
    {
      id: 'via_5',
      title: 'Kekuatan Paling Langka',
      content: 'Prudence (kehati-hatian) dan Modesty (kerendahan hati) paling jarang jadi signature strengths - hanya 8% dan 12% populasi. Humility (kerendahan hati) paling langka.',
      category: 'statistics'
    },
    {
      id: 'via_6',
      title: 'Boost Kebahagiaan 6 Bulan',
      content: 'Menggunakan signature strengths 1 minggu dapat meningkatkan kebahagiaan dan mengurangi depresi hingga 6 bulan - efek lebih tahan lama dari antidepresan.',
      category: 'wellbeing'
    },
    {
      id: 'via_7',
      title: 'Gratitude: Kekuatan #1 Kebahagiaan',
      content: 'Gratitude (syukur) adalah kekuatan karakter yang paling kuat meningkatkan kebahagiaan - 25% lebih efektif dari kekuatan lain dalam menciptakan well-being.',
      category: 'wellbeing'
    },
    {
      id: 'via_8',
      title: 'Kekuatan Berubah Seiring Waktu',
      content: 'Signature strengths dapat berubah 20-30% seiring pengalaman hidup. Trauma menurunkan Hope & Zest, sementara pencapaian meningkatkan Perseverance & Leadership.',
      category: 'development'
    }
  ],

  aiAdvantages: [
    {
      id: 'ai_1',
      title: 'Konsistensi Analisis 24/7',
      content: 'AI tidak mengalami kelelahan, mood swing, atau bias personal seperti manusia. Setiap analisis dilakukan dengan standar objektif yang sama, kapan pun Anda mengaksesnya.',
      category: 'consistency'
    },
    {
      id: 'ai_2',
      title: 'Kecepatan Pemrosesan Super',
      content: 'AI dapat menganalisis ribuan pola respons kompleks dalam hitungan detik, sementara psikolog manusia membutuhkan jam atau bahkan hari untuk analisis mendalam.',
      category: 'speed'
    },
    {
      id: 'ai_3',
      title: 'Deteksi Pola Tersembunyi',
      content: 'Machine learning dapat menemukan 847+ korelasi halus antar respons yang tidak terlihat mata manusia, mengungkap insight kepribadian yang lebih dalam.',
      category: 'pattern'
    },
    {
      id: 'ai_4',
      title: 'Bebas Bias Sosial',
      content: 'AI tidak terpengaruh stereotip gender, ras, usia, atau latar belakang sosial yang secara tidak sadar dapat mempengaruhi interpretasi psikolog manusia.',
      category: 'objectivity'
    },
    {
      id: 'ai_5',
      title: 'Pembelajaran dari Jutaan Data',
      content: 'Setiap assessment membuat AI semakin pintar. Model telah belajar dari 2.3 juta+ profil kepribadian untuk memberikan hasil yang semakin presisi.',
      category: 'learning'
    },
    {
      id: 'ai_6',
      title: 'Analisis Holistik 3-in-1',
      content: 'AI mengintegrasikan RIASEC, Big Five, dan VIA secara simultan dalam satu analisis, menciptakan profil 360° yang sulit dicapai analisis manual terpisah.',
      category: 'integration'
    },
    {
      id: 'ai_7',
      title: 'Tingkat Kepercayaan Prediksi',
      content: 'AI memberikan skor confidence 0-100% untuk setiap prediksi, memungkinkan interpretasi yang lebih nuanced dibanding penilaian ya/tidak manusia.',
      category: 'probability'
    },
    {
      id: 'ai_8',
      title: 'Skalabilitas Tanpa Batas',
      content: 'Satu model AI dapat melayani jutaan pengguna bersamaan dengan kualitas sama, tidak terbatas jam kerja atau kapasitas fisik psikolog manusia.',
      category: 'scalability'
    },
    {
      id: 'ai_9',
      title: 'Update Real-time',
      content: 'AI terus diperbarui dengan penelitian psikologi terbaru dan data global, memastikan analisis Anda selalu menggunakan standar ilmiah paling mutakhir.',
      category: 'learning'
    },
    {
      id: 'ai_10',
      title: 'Privasi & Keamanan Data',
      content: 'AI memproses data secara anonim dan terenkripsi, menghilangkan risiko bias personal atau kebocoran informasi sensitif yang mungkin terjadi pada konsultasi manusia.',
      category: 'objectivity'
    }
  ]
};

/**
 * Get a random trivia from all categories
 * @returns {Object} Random trivia object
 */
export const getRandomTrivia = () => {
  const allTrivia = [
    ...assessmentTrivia.riasec,
    ...assessmentTrivia.ocean,
    ...assessmentTrivia.via,
    ...assessmentTrivia.aiAdvantages
  ];
  
  const randomIndex = Math.floor(Math.random() * allTrivia.length);
  return allTrivia[randomIndex];
};

/**
 * Get random trivia from specific category
 * @param {string} category - 'riasec', 'ocean', 'via', or 'aiAdvantages'
 * @returns {Object} Random trivia object from specified category
 */
export const getRandomTriviaByCategory = (category) => {
  if (!assessmentTrivia[category]) {
    return getRandomTrivia();
  }
  
  const categoryTrivia = assessmentTrivia[category];
  const randomIndex = Math.floor(Math.random() * categoryTrivia.length);
  return categoryTrivia[randomIndex];
};

/**
 * Get trivia rotation for assessment stages
 * Returns different trivia for each stage to keep users engaged
 * @param {string} stage - 'processing', 'analyzing', 'preparing'
 * @returns {Object} Stage-appropriate trivia
 */
export const getTriviaForStage = (stage) => {
  switch (stage) {
    case 'processing':
      // Show general assessment trivia during processing
      return getRandomTriviaByCategory(Math.random() > 0.5 ? 'riasec' : 'ocean');
    case 'analyzing':
      // Show AI advantages during analysis
      return getRandomTriviaByCategory('aiAdvantages');
    case 'preparing':
      // Show VIA or mixed trivia during final preparation
      return getRandomTriviaByCategory(Math.random() > 0.5 ? 'via' : 'ocean');
    default:
      return getRandomTrivia();
  }
};

export default assessmentTrivia;
